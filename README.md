# Product Application with REST and gRPC APIs

This Spring Boot application provides both REST and gRPC APIs for managing products. It includes integration with a voucher service for applying discounts.

## Features

- **Dual API Support**: Both REST and gRPC endpoints for the same functionality
- **Product Management**: Create, read, update, and delete products
- **Voucher Integration**: Automatic discount application using voucher codes
- **Database Integration**: MySQL database with JPA/Hibernate
- **Testing**: Comprehensive test coverage for both APIs

## Technology Stack

- **Framework**: Spring Boot 3.5.3
- **Database**: MySQL with JPA/Hibernate
- **gRPC**: Protocol Buffers with gRPC Spring Boot Starter
- **Build Tool**: Maven
- **Java Version**: 17

## API Endpoints

### REST API (Port 9090)

- `POST /api/products` - Create a new product
- `GET /api/products` - Get all products
- `GET /api/products/{id}` - Get product by ID

### gRPC API (Port 9091)

- `CreateProduct` - Create a new product
- `GetProduct` - Get product by ID
- `GetAllProducts` - Get all products
- `UpdateProduct` - Update an existing product
- `DeleteProduct` - Delete a product

## Configuration

### Application Properties

```properties
# Database Configuration
spring.datasource.url=*********************************
spring.datasource.username=root
spring.datasource.password=!@#Zayed

# REST API Configuration
server.port=9090

# gRPC Server Configuration
grpc.server.port=9091
grpc.server.address=0.0.0.0
grpc.server.reflection-service-enabled=true
grpc.server.max-inbound-message-size=4MB
grpc.server.max-inbound-metadata-size=8KB

# Voucher Service Configuration
voucherServices.url=http://localhost:8082/voucherapi/vouchers/
```

## Getting Started

### Prerequisites

1. Java 17 or higher
2. Maven 3.6 or higher
3. MySQL database running on localhost:3306
4. Voucher service running on localhost:8082 (optional)

### Building the Application

```bash
# Generate gRPC classes from proto files
./mvnw protobuf:compile protobuf:compile-custom

# Compile the application
./mvnw clean compile

# Run tests
./mvnw test

# Package the application
./mvnw package
```

### Running the Application

```bash
./mvnw spring-boot:run
```

The application will start with:
- REST API available at `http://localhost:9090`
- gRPC API available at `localhost:9091`

## Protocol Buffer Definition

The gRPC service is defined in `src/main/proto/product.proto`:

```protobuf
service ProductService {
  rpc CreateProduct(CreateProductRequest) returns (CreateProductResponse);
  rpc GetProduct(GetProductRequest) returns (GetProductResponse);
  rpc GetAllProducts(GetAllProductsRequest) returns (GetAllProductsResponse);
  rpc UpdateProduct(UpdateProductRequest) returns (UpdateProductResponse);
  rpc DeleteProduct(DeleteProductRequest) returns (DeleteProductResponse);
}
```

## Usage Examples

### REST API Example

```bash
# Create a product
curl -X POST http://localhost:9090/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Sample Product",
    "description": "A sample product",
    "price": 99.99,
    "voucherCode": "DISCOUNT10"
  }'

# Get all products
curl http://localhost:9090/api/products

# Get product by ID
curl http://localhost:9090/api/products/1
```

### gRPC Client Example

The application includes a `ProductGrpcClient` class that demonstrates how to consume the gRPC service:

```java
ProductGrpcClient client = new ProductGrpcClient();

// Create a product
CreateProductResponse response = client.createProduct(
    "Sample Product", 
    "A sample product", 
    "99.99", 
    "DISCOUNT10"
);

// Get all products
GetAllProductsResponse allProducts = client.getAllProducts();
```

## Project Structure

```
src/
├── main/
│   ├── java/com/zayed/springrest/
│   │   ├── controller/          # REST controllers
│   │   ├── grpc/               # gRPC service implementation
│   │   ├── model/              # JPA entities
│   │   ├── repos/              # JPA repositories
│   │   └── dto/                # Data transfer objects
│   ├── proto/                  # Protocol buffer definitions
│   └── resources/
│       └── application.properties
└── test/
    └── java/com/zayed/springrest/
        └── grpc/               # gRPC service tests
```

## Dependencies

Key dependencies include:

- `spring-boot-starter-web` - REST API support
- `spring-boot-starter-data-jpa` - Database integration
- `grpc-spring-boot-starter` - gRPC server support
- `protobuf-maven-plugin` - Protocol buffer compilation

## Testing

Run the test suite:

```bash
./mvnw test
```

The tests include:
- Unit tests for gRPC service methods
- Integration tests for database operations
- Mock tests for external service calls

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 9090 and 9091 are available
2. **Database connection**: Verify MySQL is running and credentials are correct
3. **gRPC compilation**: Run `./mvnw protobuf:compile protobuf:compile-custom` if gRPC classes are missing

### Logs

Enable debug logging by adding to `application.properties`:

```properties
logging.level.com.zayed.springrest=DEBUG
logging.level.io.grpc=DEBUG
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.
