@echo off
echo Building gRPC Project...

echo Step 1: Stopping any running Java processes...
taskkill /f /im java.exe 2>nul

echo Step 2: Waiting for file locks to release...
timeout /t 3 /nobreak >nul

echo Step 3: Cleaning target directory...
if exist target (
    rmdir /s /q target 2>nul
    timeout /t 2 /nobreak >nul
)

echo Step 4: Generating gRPC classes...
call mvnw.cmd protobuf:compile protobuf:compile-custom

if %ERRORLEVEL% NEQ 0 (
    echo Failed to generate gRPC classes. Trying alternative approach...
    echo Step 5: Compiling without clean...
    call mvnw.cmd compile -DskipTests
) else (
    echo Step 5: Compiling the project...
    call mvnw.cmd compile
)

echo Step 6: Running tests...
call mvnw.cmd test

echo Build complete!
pause
