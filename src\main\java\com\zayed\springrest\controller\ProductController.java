package com.zayed.springrest.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import com.zayed.springrest.dto.Voucher;
import com.zayed.springrest.model.Product;
import com.zayed.springrest.repos.ProductRepository;

@RestController
@RequestMapping("/api")
public class ProductController {

	@Autowired
	ProductRepository repository;

	@Autowired
	RestTemplate restTemplate;

	@Value("${voucherServices.url}")
	private String voucherServiceUrl;

	@PostMapping("/products")
	public Product create(@RequestBody Product product) {
		// Apply voucher discount only if voucher code is provided
		if (product.getVoucherCode() != null && !product.getVoucherCode().isEmpty()) {
			try {
				Voucher voucher = restTemplate.getForObject(voucherServiceUrl + product.getVoucherCode(),
						Voucher.class);
				if (voucher != null) {
					product.setPrice(product.getPrice().subtract(voucher.getDiscount()));
				}
			} catch (Exception e) {
				// Log error but continue without discount
				System.err.println("Error fetching voucher: " + e.getMessage());
			}
		}

		return repository.save(product);
	}

	@GetMapping("/products")
	public List<Product> getAllProducts() {
		return repository.findAll();
	}

	@GetMapping("/products/{id}")
	public Product getProducts(@PathVariable("id") long id) {
		return repository.findById(id).orElse(null);
	}
}
