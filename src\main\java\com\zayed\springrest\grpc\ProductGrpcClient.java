package com.zayed.springrest.grpc;

import org.springframework.stereotype.Component;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;

/**
 * Example gRPC client for the Product service
 * This demonstrates how to consume the gRPC service
 */
@Component
public class ProductGrpcClient {

    private final ManagedChannel channel;
    private final ProductServiceGrpc.ProductServiceBlockingStub blockingStub;

    public ProductGrpcClient() {
        // Create a channel to connect to the gRPC server
        this.channel = ManagedChannelBuilder.forAddress("localhost", 9091)
                .usePlaintext() // Use plaintext for development (not recommended for production)
                .build();
        
        // Create a blocking stub for synchronous calls
        this.blockingStub = ProductServiceGrpc.newBlockingStub(channel);
    }

    /**
     * Example method to create a product via gRPC
     */
    public CreateProductResponse createProduct(String name, String description, String price, String voucherCode) {
        CreateProductRequest request = CreateProductRequest.newBuilder()
                .setName(name)
                .setDescription(description)
                .setPrice(price)
                .setVoucherCode(voucherCode)
                .build();

        return blockingStub.createProduct(request);
    }

    /**
     * Example method to get a product by ID via gRPC
     */
    public GetProductResponse getProduct(long id) {
        GetProductRequest request = GetProductRequest.newBuilder()
                .setId(id)
                .build();

        return blockingStub.getProduct(request);
    }

    /**
     * Example method to get all products via gRPC
     */
    public GetAllProductsResponse getAllProducts() {
        GetAllProductsRequest request = GetAllProductsRequest.newBuilder().build();
        return blockingStub.getAllProducts(request);
    }

    /**
     * Example method to update a product via gRPC
     */
    public UpdateProductResponse updateProduct(long id, String name, String description, String price, String voucherCode) {
        UpdateProductRequest request = UpdateProductRequest.newBuilder()
                .setId(id)
                .setName(name)
                .setDescription(description)
                .setPrice(price)
                .setVoucherCode(voucherCode)
                .build();

        return blockingStub.updateProduct(request);
    }

    /**
     * Example method to delete a product via gRPC
     */
    public DeleteProductResponse deleteProduct(long id) {
        DeleteProductRequest request = DeleteProductRequest.newBuilder()
                .setId(id)
                .build();

        return blockingStub.deleteProduct(request);
    }

    /**
     * Shutdown the channel when done
     */
    public void shutdown() {
        channel.shutdown();
    }

    /**
     * Example usage method - demonstrates how to use the client
     */
    public void exampleUsage() {
        try {
            // Create a product
            System.out.println("Creating a product...");
            CreateProductResponse createResponse = createProduct(
                "Sample Product", 
                "This is a sample product", 
                "99.99", 
                "DISCOUNT10"
            );
            
            if (createResponse.getSuccess()) {
                System.out.println("Product created successfully: " + createResponse.getProduct().getName());
                long productId = createResponse.getProduct().getId();
                
                // Get the product
                System.out.println("Retrieving the product...");
                GetProductResponse getResponse = getProduct(productId);
                if (getResponse.getSuccess()) {
                    System.out.println("Product retrieved: " + getResponse.getProduct().getName());
                }
                
                // Update the product
                System.out.println("Updating the product...");
                UpdateProductResponse updateResponse = updateProduct(
                    productId,
                    "Updated Product Name",
                    "Updated description",
                    "89.99",
                    ""
                );
                
                if (updateResponse.getSuccess()) {
                    System.out.println("Product updated successfully");
                }
                
                // Get all products
                System.out.println("Getting all products...");
                GetAllProductsResponse allProductsResponse = getAllProducts();
                if (allProductsResponse.getSuccess()) {
                    System.out.println("Total products: " + allProductsResponse.getProductsCount());
                }
                
                // Delete the product
                System.out.println("Deleting the product...");
                DeleteProductResponse deleteResponse = deleteProduct(productId);
                if (deleteResponse.getSuccess()) {
                    System.out.println("Product deleted successfully");
                }
            } else {
                System.err.println("Failed to create product: " + createResponse.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("Error during gRPC operations: " + e.getMessage());
        } finally {
            shutdown();
        }
    }
}
