package com.zayed.springrest.grpc;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.client.RestTemplate;
import org.springframework.stereotype.Service;

import com.zayed.springrest.dto.Voucher;
import com.zayed.springrest.model.Product;
import com.zayed.springrest.repos.ProductRepository;

// Temporarily commented out gRPC annotations until classes are generated
// import io.grpc.stub.StreamObserver;
// import net.devh.boot.grpc.server.service.GrpcService;

// @GrpcService
@Service
public class ProductGrpcService { // extends ProductServiceGrpc.ProductServiceImplBase {

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${voucherServices.url}")
    private String voucherServiceUrl;

    // Temporarily commented out until gRPC classes are generated
    /*
     * @Override
     * public void createProduct(CreateProductRequest request,
     * StreamObserver<CreateProductResponse> responseObserver) {
     * try {
     * // Create Product entity from request
     * Product product = new Product();
     * product.setName(request.getName());
     * product.setDescription(request.getDescription());
     * product.setPrice(new BigDecimal(request.getPrice()));
     * product.setVoucherCode(request.getVoucherCode());
     * 
     * // Apply voucher discount if voucher code is provided
     * if (request.getVoucherCode() != null && !request.getVoucherCode().isEmpty())
     * {
     * try {
     * Voucher voucher = restTemplate.getForObject(voucherServiceUrl +
     * request.getVoucherCode(),
     * Voucher.class);
     * if (voucher != null) {
     * product.setPrice(product.getPrice().subtract(voucher.getDiscount()));
     * }
     * } catch (Exception e) {
     * // Log error but continue without discount
     * System.err.println("Error fetching voucher: " + e.getMessage());
     * }
     * }
     * 
     * // Save product
     * Product savedProduct = productRepository.save(product);
     * 
     * // Build response
     * ProductOuterClass.Product grpcProduct = convertToGrpcProduct(savedProduct);
     * CreateProductResponse response = CreateProductResponse.newBuilder()
     * .setProduct(grpcProduct)
     * .setSuccess(true)
     * .setMessage("Product created successfully")
     * .build();
     * 
     * responseObserver.onNext(response);
     * responseObserver.onCompleted();
     * 
     * } catch (Exception e) {
     * CreateProductResponse response = CreateProductResponse.newBuilder()
     * .setSuccess(false)
     * .setMessage("Error creating product: " + e.getMessage())
     * .build();
     * 
     * responseObserver.onNext(response);
     * responseObserver.onCompleted();
     * }
     * }
     * 
     * @Override
     * public void getProduct(GetProductRequest request,
     * StreamObserver<GetProductResponse> responseObserver) {
     * try {
     * Optional<Product> productOpt = productRepository.findById(request.getId());
     * 
     * if (productOpt.isPresent()) {
     * ProductOuterClass.Product grpcProduct =
     * convertToGrpcProduct(productOpt.get());
     * GetProductResponse response = GetProductResponse.newBuilder()
     * .setProduct(grpcProduct)
     * .setSuccess(true)
     * .setMessage("Product found")
     * .build();
     * 
     * responseObserver.onNext(response);
     * } else {
     * GetProductResponse response = GetProductResponse.newBuilder()
     * .setSuccess(false)
     * .setMessage("Product not found")
     * .build();
     * 
     * responseObserver.onNext(response);
     * }
     * 
     * responseObserver.onCompleted();
     * 
     * } catch (Exception e) {
     * GetProductResponse response = GetProductResponse.newBuilder()
     * .setSuccess(false)
     * .setMessage("Error retrieving product: " + e.getMessage())
     * .build();
     * 
     * responseObserver.onNext(response);
     * responseObserver.onCompleted();
     * }
     * }
     * 
     * @Override
     * public void getAllProducts(GetAllProductsRequest request,
     * StreamObserver<GetAllProductsResponse> responseObserver) {
     * try {
     * List<Product> products = productRepository.findAll();
     * 
     * GetAllProductsResponse.Builder responseBuilder =
     * GetAllProductsResponse.newBuilder()
     * .setSuccess(true)
     * .setMessage("Products retrieved successfully");
     * 
     * for (Product product : products) {
     * ProductOuterClass.Product grpcProduct = convertToGrpcProduct(product);
     * responseBuilder.addProducts(grpcProduct);
     * }
     * 
     * responseObserver.onNext(responseBuilder.build());
     * responseObserver.onCompleted();
     * 
     * } catch (Exception e) {
     * GetAllProductsResponse response = GetAllProductsResponse.newBuilder()
     * .setSuccess(false)
     * .setMessage("Error retrieving products: " + e.getMessage())
     * .build();
     * 
     * responseObserver.onNext(response);
     * responseObserver.onCompleted();
     * }
     * }
     * 
     * @Override
     * public void updateProduct(UpdateProductRequest request,
     * StreamObserver<UpdateProductResponse> responseObserver) {
     * try {
     * Optional<Product> productOpt = productRepository.findById(request.getId());
     * 
     * if (productOpt.isPresent()) {
     * Product product = productOpt.get();
     * product.setName(request.getName());
     * product.setDescription(request.getDescription());
     * product.setPrice(new BigDecimal(request.getPrice()));
     * product.setVoucherCode(request.getVoucherCode());
     * 
     * // Apply voucher discount if voucher code is provided
     * if (request.getVoucherCode() != null && !request.getVoucherCode().isEmpty())
     * {
     * try {
     * Voucher voucher = restTemplate.getForObject(voucherServiceUrl +
     * request.getVoucherCode(),
     * Voucher.class);
     * if (voucher != null) {
     * product.setPrice(product.getPrice().subtract(voucher.getDiscount()));
     * }
     * } catch (Exception e) {
     * // Log error but continue without discount
     * System.err.println("Error fetching voucher: " + e.getMessage());
     * }
     * }
     * 
     * Product updatedProduct = productRepository.save(product);
     * 
     * ProductOuterClass.Product grpcProduct = convertToGrpcProduct(updatedProduct);
     * UpdateProductResponse response = UpdateProductResponse.newBuilder()
     * .setProduct(grpcProduct)
     * .setSuccess(true)
     * .setMessage("Product updated successfully")
     * .build();
     * 
     * responseObserver.onNext(response);
     * } else {
     * UpdateProductResponse response = UpdateProductResponse.newBuilder()
     * .setSuccess(false)
     * .setMessage("Product not found")
     * .build();
     * 
     * responseObserver.onNext(response);
     * }
     * 
     * responseObserver.onCompleted();
     * 
     * } catch (Exception e) {
     * UpdateProductResponse response = UpdateProductResponse.newBuilder()
     * .setSuccess(false)
     * .setMessage("Error updating product: " + e.getMessage())
     * .build();
     * 
     * responseObserver.onNext(response);
     * responseObserver.onCompleted();
     * }
     * }
     * 
     * @Override
     * public void deleteProduct(DeleteProductRequest request,
     * StreamObserver<DeleteProductResponse> responseObserver) {
     * try {
     * if (productRepository.existsById(request.getId())) {
     * productRepository.deleteById(request.getId());
     * 
     * DeleteProductResponse response = DeleteProductResponse.newBuilder()
     * .setSuccess(true)
     * .setMessage("Product deleted successfully")
     * .build();
     * 
     * responseObserver.onNext(response);
     * } else {
     * DeleteProductResponse response = DeleteProductResponse.newBuilder()
     * .setSuccess(false)
     * .setMessage("Product not found")
     * .build();
     * 
     * responseObserver.onNext(response);
     * }
     * 
     * responseObserver.onCompleted();
     * 
     * } catch (Exception e) {
     * DeleteProductResponse response = DeleteProductResponse.newBuilder()
     * .setSuccess(false)
     * .setMessage("Error deleting product: " + e.getMessage())
     * .build();
     * 
     * responseObserver.onNext(response);
     * responseObserver.onCompleted();
     * }
     * }
     * 
     * private ProductOuterClass.Product convertToGrpcProduct(Product product) {
     * return ProductOuterClass.Product.newBuilder()
     * .setId(product.getId())
     * .setName(product.getName())
     * .setDescription(product.getDescription())
     * .setPrice(product.getPrice().toString())
     * .setVoucherCode(product.getVoucherCode() != null ? product.getVoucherCode() :
     * "")
     * .build();
     * }
     */

    // Simple method to test the service is working
    public String getServiceStatus() {
        return "ProductGrpcService is ready - waiting for gRPC classes to be generated";
    }
}
