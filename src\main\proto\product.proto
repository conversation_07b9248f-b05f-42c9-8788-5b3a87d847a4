syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.zayed.springrest.grpc";
option java_outer_classname = "ProductProto";

package product;

// Product message definition
message Product {
  int64 id = 1;
  string name = 2;
  string description = 3;
  string price = 4; // Using string to handle BigDecimal
  string voucher_code = 5;
}

// Request message for creating a product
message CreateProductRequest {
  string name = 1;
  string description = 2;
  string price = 3; // Using string to handle BigDecimal
  string voucher_code = 4;
}

// Response message for creating a product
message CreateProductResponse {
  Product product = 1;
  bool success = 2;
  string message = 3;
}

// Request message for getting a product by ID
message GetProductRequest {
  int64 id = 1;
}

// Response message for getting a product
message GetProductResponse {
  Product product = 1;
  bool success = 2;
  string message = 3;
}

// Request message for getting all products
message GetAllProductsRequest {
  // Empty request - no parameters needed
}

// Response message for getting all products
message GetAllProductsResponse {
  repeated Product products = 1;
  bool success = 2;
  string message = 3;
}

// Request message for updating a product
message UpdateProductRequest {
  int64 id = 1;
  string name = 2;
  string description = 3;
  string price = 4;
  string voucher_code = 5;
}

// Response message for updating a product
message UpdateProductResponse {
  Product product = 1;
  bool success = 2;
  string message = 3;
}

// Request message for deleting a product
message DeleteProductRequest {
  int64 id = 1;
}

// Response message for deleting a product
message DeleteProductResponse {
  bool success = 1;
  string message = 2;
}

// Product gRPC service definition
service ProductService {
  // Create a new product
  rpc CreateProduct(CreateProductRequest) returns (CreateProductResponse);
  
  // Get a product by ID
  rpc GetProduct(GetProductRequest) returns (GetProductResponse);
  
  // Get all products
  rpc GetAllProducts(GetAllProductsRequest) returns (GetAllProductsResponse);
  
  // Update an existing product
  rpc UpdateProduct(UpdateProductRequest) returns (UpdateProductResponse);
  
  // Delete a product
  rpc DeleteProduct(DeleteProductRequest) returns (DeleteProductResponse);
}
