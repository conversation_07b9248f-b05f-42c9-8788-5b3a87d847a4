package com.zayed.springrest.grpc;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import com.zayed.springrest.dto.Voucher;
import com.zayed.springrest.model.Product;
import com.zayed.springrest.repos.ProductRepository;

import io.grpc.stub.StreamObserver;

/**
 * Integration tests for ProductGrpcService
 * Note: These tests use mocks since the generated gRPC classes are not available yet
 */
@ExtendWith(MockitoExtension.class)
public class ProductGrpcServiceTest {

    @Mock
    private ProductRepository productRepository;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private StreamObserver<CreateProductResponse> createResponseObserver;

    @Mock
    private StreamObserver<GetProductResponse> getResponseObserver;

    @Mock
    private StreamObserver<GetAllProductsResponse> getAllResponseObserver;

    @InjectMocks
    private ProductGrpcService productGrpcService;

    private Product sampleProduct;
    private Voucher sampleVoucher;

    @BeforeEach
    void setUp() {
        // Set up sample data
        sampleProduct = new Product();
        sampleProduct.setId(1L);
        sampleProduct.setName("Test Product");
        sampleProduct.setDescription("Test Description");
        sampleProduct.setPrice(new BigDecimal("100.00"));
        sampleProduct.setVoucherCode("TEST10");

        sampleVoucher = new Voucher();
        sampleVoucher.setId(1L);
        sampleVoucher.setCode("TEST10");
        sampleVoucher.setDiscount(new BigDecimal("10.00"));
        sampleVoucher.setExpireDate("2024-12-31");
    }

    @Test
    void testCreateProductSuccess() {
        // Arrange
        when(restTemplate.getForObject(anyString(), any(Class.class)))
                .thenReturn(sampleVoucher);
        when(productRepository.save(any(Product.class)))
                .thenReturn(sampleProduct);

        CreateProductRequest request = CreateProductRequest.newBuilder()
                .setName("Test Product")
                .setDescription("Test Description")
                .setPrice("100.00")
                .setVoucherCode("TEST10")
                .build();

        // Act & Assert
        assertDoesNotThrow(() -> {
            productGrpcService.createProduct(request, createResponseObserver);
        });
    }

    @Test
    void testGetProductSuccess() {
        // Arrange
        when(productRepository.findById(1L))
                .thenReturn(Optional.of(sampleProduct));

        GetProductRequest request = GetProductRequest.newBuilder()
                .setId(1L)
                .build();

        // Act & Assert
        assertDoesNotThrow(() -> {
            productGrpcService.getProduct(request, getResponseObserver);
        });
    }

    @Test
    void testGetProductNotFound() {
        // Arrange
        when(productRepository.findById(999L))
                .thenReturn(Optional.empty());

        GetProductRequest request = GetProductRequest.newBuilder()
                .setId(999L)
                .build();

        // Act & Assert
        assertDoesNotThrow(() -> {
            productGrpcService.getProduct(request, getResponseObserver);
        });
    }

    @Test
    void testGetAllProductsSuccess() {
        // Arrange
        Product product2 = new Product();
        product2.setId(2L);
        product2.setName("Test Product 2");
        product2.setDescription("Test Description 2");
        product2.setPrice(new BigDecimal("200.00"));

        when(productRepository.findAll())
                .thenReturn(Arrays.asList(sampleProduct, product2));

        GetAllProductsRequest request = GetAllProductsRequest.newBuilder().build();

        // Act & Assert
        assertDoesNotThrow(() -> {
            productGrpcService.getAllProducts(request, getAllResponseObserver);
        });
    }

    @Test
    void testUpdateProductSuccess() {
        // Arrange
        when(productRepository.findById(1L))
                .thenReturn(Optional.of(sampleProduct));
        when(productRepository.save(any(Product.class)))
                .thenReturn(sampleProduct);

        UpdateProductRequest request = UpdateProductRequest.newBuilder()
                .setId(1L)
                .setName("Updated Product")
                .setDescription("Updated Description")
                .setPrice("150.00")
                .setVoucherCode("")
                .build();

        StreamObserver<UpdateProductResponse> updateResponseObserver = 
                mock(StreamObserver.class);

        // Act & Assert
        assertDoesNotThrow(() -> {
            productGrpcService.updateProduct(request, updateResponseObserver);
        });
    }

    @Test
    void testDeleteProductSuccess() {
        // Arrange
        when(productRepository.existsById(1L))
                .thenReturn(true);

        DeleteProductRequest request = DeleteProductRequest.newBuilder()
                .setId(1L)
                .build();

        StreamObserver<DeleteProductResponse> deleteResponseObserver = 
                mock(StreamObserver.class);

        // Act & Assert
        assertDoesNotThrow(() -> {
            productGrpcService.deleteProduct(request, deleteResponseObserver);
        });
    }

    @Test
    void testDeleteProductNotFound() {
        // Arrange
        when(productRepository.existsById(999L))
                .thenReturn(false);

        DeleteProductRequest request = DeleteProductRequest.newBuilder()
                .setId(999L)
                .build();

        StreamObserver<DeleteProductResponse> deleteResponseObserver = 
                mock(StreamObserver.class);

        // Act & Assert
        assertDoesNotThrow(() -> {
            productGrpcService.deleteProduct(request, deleteResponseObserver);
        });
    }

    /**
     * Test the conversion utility method
     */
    @Test
    void testConvertToGrpcProduct() {
        // This test would verify the conversion method once the generated classes are available
        // For now, we just ensure the service can be instantiated
        assertNotNull(productGrpcService);
    }
}
